from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.adapter import BaseAdapter
from app.integrations.schemas import CRMAccountAccessData


class BaseCRMAdapter(BaseAdapter, ABC):
    @abstractmethod
    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    def get_account(self, account_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    def update_account(self, account_id: str, fields: dict[str, Any]) -> dict[str, Any]:
        pass

    @abstractmethod
    def resolve_account_access(self, crm_user_id: str) -> list[CRMAccountAccessData]:
        pass

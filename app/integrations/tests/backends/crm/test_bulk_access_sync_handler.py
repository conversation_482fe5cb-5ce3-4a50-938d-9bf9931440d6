import uuid
from unittest.mock import call

import pytest

from app.integrations.backends.crm.bulk_access_sync_handler import (
    BulkAccountAccessSyncHandler,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
    ICredentialsResolver,
)
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.types import IntegrationSource


class MockCRMAdapter(BaseCRMAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    def get_opportunity(self, opportunity_id: str):
        pass

    def update_opportunity(self, opportunity_id: str, fields):
        pass

    def list_opportunities_by_account(self, account_id, limit=100, offset=0):
        pass

    def get_account(self, account_id: str):
        pass

    def update_account(self, account_id: str, fields):
        pass

    def resolve_account_access(self, crm_user_id: str):
        pass


@pytest.fixture
def mock_credentials_resolver(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "username": "<EMAIL>",
        "password": "password123",
        "security_token": "token123",
    }
    mock_credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_credentials_resolver.get_credentials.return_value = mock_credentials
    return mock_credentials_resolver


@pytest.fixture
def mock_oauth_credentials_resolver(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "fake-token",
        "instance_url": "https://fake.salesforce.com",
    }
    mock_credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_credentials_resolver.get_credentials.return_value = mock_credentials
    return mock_credentials_resolver


@pytest.fixture
def db_session_factory():
    def factory():
        return None

    return factory


@pytest.fixture
def crm_store_mock(mocker):
    return mocker.Mock()


@pytest.fixture
def sync_handler(mock_credentials_resolver, db_session_factory, crm_store_mock):
    tenant_id = uuid.uuid4()
    return BulkAccountAccessSyncHandler(
        tenant_id=tenant_id,
        source=IntegrationSource.SALESFORCE,
        db_session_factory=db_session_factory,
        adapter_factory=lambda credentials: MockCRMAdapter(credentials),
        crm_store=crm_store_mock,
        credentials_resolver=mock_credentials_resolver,
    )


def test_constructor(mock_credentials_resolver, db_session_factory, crm_store_mock):
    tenant_id = uuid.uuid4()

    handler = BulkAccountAccessSyncHandler(
        tenant_id=tenant_id,
        source=IntegrationSource.SALESFORCE,
        db_session_factory=db_session_factory,
        adapter_factory=lambda credentials: MockCRMAdapter(credentials),
        crm_store=crm_store_mock,
        credentials_resolver=mock_credentials_resolver,
    )

    assert handler._tenant_id == tenant_id
    assert handler._db_session_factory == db_session_factory
    assert handler._credentials_resolver == mock_credentials_resolver
    assert handler._crm_store == crm_store_mock
    assert handler._synchronizer_cache == {}


def test_create_synchronizer_with_password_auth(
    mocker, sync_handler, mock_credentials_resolver
):
    mocker.patch(
        "app.integrations.backends.crm.bulk_access_sync_handler.AccountAccessSynchronizer",
        return_value=mocker.Mock(),
    )

    result = sync_handler._create_synchronizer(mock_credentials_resolver)

    from app.integrations.backends.crm.bulk_access_sync_handler import (
        AccountAccessSynchronizer,
    )

    # Verify that get_credentials was called
    mock_credentials_resolver.get_credentials.assert_called_once_with(
        IntegrationSource.SALESFORCE
    )

    AccountAccessSynchronizer.assert_called_once()
    assert result is not None


def test_create_synchronizer_with_oauth(
    mocker, mock_oauth_credentials_resolver, db_session_factory, crm_store_mock
):
    tenant_id = uuid.uuid4()

    handler = BulkAccountAccessSyncHandler(
        tenant_id=tenant_id,
        source=IntegrationSource.SALESFORCE,
        db_session_factory=db_session_factory,
        adapter_factory=lambda credentials: MockCRMAdapter(credentials),
        crm_store=crm_store_mock,
        credentials_resolver=mock_oauth_credentials_resolver,
    )

    mocker.patch(
        "app.integrations.backends.crm.bulk_access_sync_handler.AccountAccessSynchronizer",
        return_value=mocker.Mock(),
    )

    result = handler._create_synchronizer(mock_oauth_credentials_resolver)

    from app.integrations.backends.crm.bulk_access_sync_handler import (
        AccountAccessSynchronizer,
    )

    # Verify that get_credentials was called
    mock_oauth_credentials_resolver.get_credentials.assert_called_once_with(
        IntegrationSource.SALESFORCE
    )

    AccountAccessSynchronizer.assert_called_once()
    assert result is not None


def test_get_or_create_synchronizer_creates_new(
    mocker, sync_handler, mock_credentials_resolver
):
    mock_synchronizer = mocker.Mock()
    mocker.patch.object(
        sync_handler, "_create_synchronizer", return_value=mock_synchronizer
    )

    result = sync_handler._get_or_create_synchronizer(mock_credentials_resolver)

    assert result == mock_synchronizer
    sync_handler._create_synchronizer.assert_called_once_with(mock_credentials_resolver)

    resolver_id = id(mock_credentials_resolver)
    assert sync_handler._synchronizer_cache[resolver_id] == mock_synchronizer


def test_get_or_create_synchronizer_returns_from_cache(
    mocker, sync_handler, mock_credentials_resolver
):
    mock_synchronizer = mocker.Mock()
    resolver_id = id(mock_credentials_resolver)
    sync_handler._synchronizer_cache[resolver_id] = mock_synchronizer

    create_mock = mocker.patch.object(sync_handler, "_create_synchronizer")

    result = sync_handler._get_or_create_synchronizer(mock_credentials_resolver)

    assert result == mock_synchronizer
    create_mock.assert_not_called()


def test_execute_with_default_resolver(mocker, sync_handler):
    mock_synchronizer = mocker.Mock()
    mocker.patch.object(
        sync_handler, "_get_or_create_synchronizer", return_value=mock_synchronizer
    )

    mock_stage = mocker.Mock()
    mocker.patch(
        "app.integrations.backends.crm.bulk_access_sync_handler.AccountAccessSyncStage",
        return_value=mock_stage,
    )

    mock_pipeline = mocker.Mock()
    mock_pipeline.run.return_value = {"status": "success"}
    mocker.patch(
        "app.integrations.backends.crm.bulk_access_sync_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    user_ids = ["user1", "user2"]
    result = sync_handler.execute(
        crm_user_ids=user_ids,
        interval_seconds=600,
        daemon_mode=False,
    )

    assert sync_handler._get_or_create_synchronizer.call_count == 2
    sync_handler._get_or_create_synchronizer.assert_has_calls(
        [
            call(sync_handler._credentials_resolver),
            call(sync_handler._credentials_resolver),
        ]
    )

    from app.integrations.backends.crm.bulk_access_sync_handler import (
        AccountAccessSyncStage,
    )

    AccountAccessSyncStage.assert_called_once()
    _, kwargs = AccountAccessSyncStage.call_args
    assert kwargs["tenant_id"] == sync_handler._tenant_id
    assert kwargs["interval_seconds"] == 600
    assert len(kwargs["user_synchronizers"]) == 2
    assert kwargs["user_synchronizers"] == [
        ("user1", mock_synchronizer),
        ("user2", mock_synchronizer),
    ]

    mock_pipeline.add_stage.assert_called_once_with(mock_stage)
    mock_pipeline.run.assert_called_once()
    assert result == {"status": "success"}


def test_execute_with_custom_resolver(mocker, sync_handler):
    mock_synchronizer1 = mocker.Mock(name="synchronizer1")
    mock_synchronizer2 = mocker.Mock(name="synchronizer2")

    get_or_create_mock = mocker.patch.object(
        sync_handler,
        "_get_or_create_synchronizer",
        side_effect=[mock_synchronizer1, mock_synchronizer2],
    )

    mock_stage = mocker.Mock()
    mocker.patch(
        "app.integrations.backends.crm.bulk_access_sync_handler.AccountAccessSyncStage",
        return_value=mock_stage,
    )

    mock_pipeline = mocker.Mock()
    mock_pipeline.run.return_value = {"status": "success"}
    mocker.patch(
        "app.integrations.backends.crm.bulk_access_sync_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    resolver1 = mocker.Mock(name="resolver1")
    resolver2 = mocker.Mock(name="resolver2")

    def custom_get_credentials_resolver(user_id):
        if user_id == "user1":
            return resolver1
        else:
            return resolver2

    user_ids = ["user1", "user2"]
    result = sync_handler.execute(
        crm_user_ids=user_ids,
        get_credentials_resolver=custom_get_credentials_resolver,
        interval_seconds=600,
        daemon_mode=False,
    )

    assert get_or_create_mock.call_count == 2
    get_or_create_mock.assert_has_calls([call(resolver1), call(resolver2)])

    from app.integrations.backends.crm.bulk_access_sync_handler import (
        AccountAccessSyncStage,
    )

    AccountAccessSyncStage.assert_called_once()
    _, kwargs = AccountAccessSyncStage.call_args
    assert kwargs["tenant_id"] == sync_handler._tenant_id
    assert kwargs["interval_seconds"] == 600
    assert len(kwargs["user_synchronizers"]) == 2
    assert kwargs["user_synchronizers"] == [
        ("user1", mock_synchronizer1),
        ("user2", mock_synchronizer2),
    ]

    mock_pipeline.add_stage.assert_called_once_with(mock_stage)
    mock_pipeline.run.assert_called_once()
    assert result == {"status": "success"}


def test_execute_daemon_mode(mocker, sync_handler):
    mock_synchronizer = mocker.Mock()
    mocker.patch.object(
        sync_handler, "_get_or_create_synchronizer", return_value=mock_synchronizer
    )

    mock_stage = mocker.Mock()
    mocker.patch(
        "app.integrations.backends.crm.bulk_access_sync_handler.AccountAccessSyncStage",
        return_value=mock_stage,
    )

    mock_pipeline = mocker.Mock()
    mocker.patch(
        "app.integrations.backends.crm.bulk_access_sync_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    user_ids = ["user1"]
    result = sync_handler.execute(
        crm_user_ids=user_ids,
        daemon_mode=True,
    )

    mock_pipeline.add_stage.assert_called_once_with(mock_stage)
    mock_pipeline.start_daemon.assert_called_once()
    assert result == {"status": "daemon_stopped"}


def test_execute_no_resolver(sync_handler):
    sync_handler._credentials_resolver = None

    with pytest.raises(
        ValueError, match="Either get_credentials_resolver must be provided"
    ):
        sync_handler.execute(
            crm_user_ids=["user1"],
        )


def test_execute_user_resolver_returns_none(sync_handler):
    def bad_resolver(_):
        return None

    with pytest.raises(ValueError, match="Missing credentials resolver for user"):
        sync_handler.execute(
            crm_user_ids=["user1"],
            get_credentials_resolver=bad_resolver,
        )


def test_execute_no_valid_synchronizers(mocker, sync_handler):
    mocker.patch.object(sync_handler, "_get_or_create_synchronizer", return_value=None)

    with pytest.raises(ValueError, match="No valid synchronizers could be created"):
        sync_handler.execute(
            crm_user_ids=["user1", "user2"],
        )

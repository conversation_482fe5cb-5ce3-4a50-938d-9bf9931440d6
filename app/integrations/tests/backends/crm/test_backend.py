import uuid

import pytest

from app.integrations.backends.crm.backend import CRMBackend
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.context import IntegrationContext
from app.integrations.schemas import CR<PERSON><PERSON>untAccessData, CRMAccountAccessSlice
from app.integrations.types import IntegrationSource


class MockCRMAdapter(BaseCRMAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self.mock_data = {
            "opportunities": {
                "opp1": {"Id": "opp1", "Name": "Test Opportunity"},
            },
            "accounts": {
                "acc1": {"Id": "acc1", "Name": "Test Account"},
            },
        }

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    def get_opportunity(self, opportunity_id: str):
        return self.mock_data["opportunities"].get(opportunity_id)

    def update_opportunity(self, opportunity_id: str, fields):
        opportunity = self.mock_data["opportunities"].get(opportunity_id, {})
        opportunity.update(fields)
        return opportunity

    def list_opportunities_by_account(self, account_id, limit=100, offset=0):
        _ = limit, offset
        return [
            opp
            for opp in self.mock_data["opportunities"].values()
            if opp.get("AccountId") == account_id
        ]

    def get_account(self, account_id: str):
        return self.mock_data["accounts"].get(account_id)

    def update_account(self, account_id: str, fields):
        account = self.mock_data["accounts"].get(account_id, {})
        account.update(fields)
        return account

    def resolve_account_access(self, crm_user_id: str):
        _ = crm_user_id
        return [
            CRMAccountAccessData(
                account_id="acc1",
                account_name="Test Account",
                access_type="owner",
                access_role=None,
            )
        ]


@pytest.fixture
def db_session_factory(mocker):
    mock_session = mocker.MagicMock()
    return lambda: mock_session


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {
        "username": "test_user",
        "password": "test_pass",
    }
    return mock_creds


@pytest.fixture
def credentials_resolver(mocker, mock_credentials):
    mock_resolver = mocker.MagicMock()
    mock_resolver.get_credentials.return_value = mock_credentials
    return mock_resolver


@pytest.fixture
def context(tenant_id, db_session_factory, credentials_resolver):
    return IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=credentials_resolver,
    )


@pytest.fixture
def crm_store_mock(mocker):
    mock_store = mocker.MagicMock()
    return mock_store


@pytest.fixture
def crm_backend(mocker, context, crm_store_mock):
    mocker.patch(
        "app.integrations.backends.crm.backend.PostgresCRMStore",
        return_value=crm_store_mock,
    )

    backend = CRMBackend(
        context=context,
        adapter_class=MockCRMAdapter,
        source=IntegrationSource.SALESFORCE,
    )
    return backend


def test_init(context):
    backend = CRMBackend(
        context=context,
        adapter_class=MockCRMAdapter,
        source=IntegrationSource.SALESFORCE,
    )

    assert backend.context == context
    assert backend.tenant_id == context.tenant_id
    assert backend.source == IntegrationSource.SALESFORCE

    assert not hasattr(backend, "_crm_store")


def test_lazy_crm_store_initialization(mocker, context):
    postgres_store_mock = mocker.MagicMock()
    mocker.patch(
        "app.integrations.backends.crm.backend.PostgresCRMStore",
        return_value=postgres_store_mock,
    )

    backend = CRMBackend(
        context=context,
        adapter_class=MockCRMAdapter,
        source=IntegrationSource.SALESFORCE,
    )

    assert not hasattr(backend, "_crm_store")

    backend.crm_store  # noqa: B018

    assert hasattr(backend, "_crm_store")
    assert backend._crm_store is postgres_store_mock

    store_again = backend.crm_store
    assert store_again is postgres_store_mock


def test_init_with_null_credentials_resolver(context):
    context.credentials_resolver = None

    backend = CRMBackend(
        context=context,
        adapter_class=MockCRMAdapter,
        source=IntegrationSource.SALESFORCE,
    )

    assert backend.context.credentials_resolver is None


def test_get_opportunity(crm_backend):
    result = crm_backend.get_opportunity("opp1")
    assert result == {"Id": "opp1", "Name": "Test Opportunity"}


def test_update_opportunity(crm_backend):
    result = crm_backend.update_opportunity("opp1", {"Name": "Updated Opportunity"})
    assert result["Name"] == "Updated Opportunity"


def test_list_opportunities_by_account(crm_backend):
    result = crm_backend.list_opportunities_by_account("acc1", limit=10, offset=0)
    assert isinstance(result, list)


def test_get_account(crm_backend):
    result = crm_backend.get_account("acc1")
    assert result == {"Id": "acc1", "Name": "Test Account"}


def test_update_account(crm_backend):
    result = crm_backend.update_account("acc1", {"Name": "Updated Account"})
    assert result["Name"] == "Updated Account"


def test_list_account_access(crm_backend, crm_store_mock):
    access_data = [
        CRMAccountAccessData(
            account_id="acc1",
            account_name="Test Account",
            access_type="owner",
            access_role=None,
        ),
        CRMAccountAccessData(
            account_id="acc2",
            account_name="Another Account",
            access_type="team",
            access_role="Member",
        ),
    ]

    crm_store_mock.get_user_account_access.return_value = CRMAccountAccessSlice(
        user_id="user1", accounts=access_data
    )

    result = crm_backend.list_account_access("user1", limit=10, offset=0)

    crm_store_mock.get_user_account_access.assert_called_once_with("user1")

    assert len(result) == 2
    assert result[0]["Id"] == "acc1"
    assert result[0]["Name"] == "Test Account"
    assert result[0]["AccessType"] == "owner"
    assert result[0]["AccessRole"] == ""

    assert result[1]["Id"] == "acc2"
    assert result[1]["Name"] == "Another Account"
    assert result[1]["AccessType"] == "team"
    assert result[1]["AccessRole"] == "Member"


def test_list_account_access_pagination(crm_backend, crm_store_mock):
    access_data = [
        CRMAccountAccessData(
            account_id=f"acc{i}",
            account_name=f"Account {i}",
            access_type="owner",
            access_role=None,
        )
        for i in range(10)
    ]

    crm_store_mock.get_user_account_access.return_value = CRMAccountAccessSlice(
        user_id="user1", accounts=access_data
    )

    result = crm_backend.list_account_access("user1", limit=3, offset=2)

    assert len(result) == 3
    assert result[0]["Id"] == "acc2"
    assert result[1]["Id"] == "acc3"
    assert result[2]["Id"] == "acc4"


def test_bulk_sync_account_access(mocker, context, crm_store_mock):
    mock_handler_instance = mocker.Mock()
    mock_handler_instance.execute.return_value = {"status": "success"}

    mock_handler_class = mocker.patch(
        "app.integrations.backends.crm.backend.BulkAccountAccessSyncHandler"
    )
    mock_handler_class.return_value = mock_handler_instance

    mocker.patch(
        "app.integrations.backends.crm.backend.PostgresCRMStore",
        return_value=crm_store_mock,
    )

    mocker.patch(
        "app.integrations.backends.crm.backend.cast", side_effect=lambda _, value: value
    )

    backend = CRMBackend(
        context=context,
        adapter_class=MockCRMAdapter,
        source=IntegrationSource.SALESFORCE,
    )

    user_ids = ["user1", "user2"]
    interval_seconds = 600
    daemon_mode = True

    result = backend.bulk_sync_account_access(
        crm_user_ids=user_ids,
        interval_seconds=interval_seconds,
        daemon_mode=daemon_mode,
    )

    mock_handler_class.assert_called_once()
    _, kwargs = mock_handler_class.call_args
    assert kwargs["tenant_id"] == context.tenant_id
    assert kwargs["db_session_factory"] == context.db_session_factory
    assert "adapter_factory" in kwargs
    assert kwargs["crm_store"] == crm_store_mock

    assert mock_handler_instance.execute.call_count == 1
    call_args = mock_handler_instance.execute.call_args.kwargs
    assert call_args["crm_user_ids"] == user_ids
    assert call_args["interval_seconds"] == interval_seconds
    assert call_args["daemon_mode"] == daemon_mode

    assert result == {"status": "success"}

import pytest

from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.handles import (
    BaseHandle,
    CRMHandle,
    CRMSyncerHandle,
    MessagingHandle,
    MessagingIngestorHandle,
    MessagingProcessorHandle,
)
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_crm_backend(mocker):
    return mocker.Mock(spec=BaseCRMBackend)


@pytest.fixture
def mock_messaging_backend(mocker):
    return mocker.Mock(spec=BaseMessagingBackend)


def test_base_handle_initialization(mock_crm_backend):
    mock_crm_backend.source = IntegrationSource.SALESFORCE
    handle = BaseHandle(mock_crm_backend)

    assert handle._backend == mock_crm_backend
    assert handle.source == IntegrationSource.SALESFORCE


def test_crm_handle_initialization(mock_crm_backend):
    mock_crm_backend.source = IntegrationSource.SALESFORCE
    handle = CRMHandle(mock_crm_backend)

    assert handle._backend == mock_crm_backend
    assert handle.source == IntegrationSource.SALESFORCE


def test_crm_handle_get_opportunity(mock_crm_backend):
    mock_crm_backend.get_opportunity.return_value = {"id": "123", "name": "Test Opp"}
    handle = CRMHandle(mock_crm_backend)

    result = handle.get_opportunity("123")

    mock_crm_backend.get_opportunity.assert_called_once_with("123")
    assert result == {"id": "123", "name": "Test Opp"}


def test_crm_handle_update_opportunity(mock_crm_backend):
    fields = {"name": "Updated Opp"}
    mock_crm_backend.update_opportunity.return_value = {
        "id": "123",
        "name": "Updated Opp",
    }
    handle = CRMHandle(mock_crm_backend)

    result = handle.update_opportunity("123", fields)

    mock_crm_backend.update_opportunity.assert_called_once_with("123", fields)
    assert result == {"id": "123", "name": "Updated Opp"}


def test_crm_handle_list_opportunities_by_account(mock_crm_backend):
    expected_opportunities = [{"id": "123"}, {"id": "456"}]
    mock_crm_backend.list_opportunities_by_account.return_value = expected_opportunities
    handle = CRMHandle(mock_crm_backend)

    result = handle.list_opportunities_by_account("acc123", limit=50, offset=10)

    mock_crm_backend.list_opportunities_by_account.assert_called_once_with(
        "acc123", 50, 10
    )
    assert result == expected_opportunities


def test_crm_handle_list_opportunities_by_account_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    handle.list_opportunities_by_account("acc123")

    mock_crm_backend.list_opportunities_by_account.assert_called_once_with(
        "acc123", 100, 0
    )


def test_crm_handle_get_account(mock_crm_backend):
    expected_account = {"id": "acc123", "name": "Test Account"}
    mock_crm_backend.get_account.return_value = expected_account
    handle = CRMHandle(mock_crm_backend)

    result = handle.get_account("acc123")

    mock_crm_backend.get_account.assert_called_once_with("acc123")
    assert result == expected_account


def test_crm_handle_update_account(mock_crm_backend):
    fields = {"name": "Updated Account"}
    mock_crm_backend.update_account.return_value = {
        "id": "acc123",
        "name": "Updated Account",
    }
    handle = CRMHandle(mock_crm_backend)

    result = handle.update_account("acc123", fields)

    mock_crm_backend.update_account.assert_called_once_with("acc123", fields)
    assert result == {"id": "acc123", "name": "Updated Account"}


def test_crm_handle_list_account_access(mock_crm_backend):
    expected_access = [{"account_id": "acc123", "access_type": "read"}]
    mock_crm_backend.list_account_access.return_value = expected_access
    handle = CRMHandle(mock_crm_backend)

    result = handle.list_account_access("user123", limit=50, offset=10)

    mock_crm_backend.list_account_access.assert_called_once_with("user123", 50, 10)
    assert result == expected_access


def test_crm_handle_list_account_access_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    handle.list_account_access("user123")

    mock_crm_backend.list_account_access.assert_called_once_with("user123", 100, 0)


def test_crm_syncer_handle_initialization(mock_crm_backend):
    mock_crm_backend.source = IntegrationSource.SALESFORCE
    handle = CRMSyncerHandle(mock_crm_backend)

    assert handle._backend == mock_crm_backend
    assert handle.source == IntegrationSource.SALESFORCE


def test_crm_syncer_handle_bulk_sync_account_access(mock_crm_backend):
    user_ids = ["user1", "user2"]
    expected_result = {"status": "started", "job_id": "job123"}
    mock_crm_backend.bulk_sync_account_access.return_value = expected_result
    handle = CRMSyncerHandle(mock_crm_backend)

    result = handle.bulk_sync_account_access(
        user_ids, get_credentials_resolver=None, interval_seconds=600, daemon_mode=True
    )

    mock_crm_backend.bulk_sync_account_access.assert_called_once_with(
        user_ids, None, 600, True
    )
    assert result == expected_result


def test_crm_syncer_handle_bulk_sync_account_access_defaults(mock_crm_backend):
    user_ids = ["user1", "user2"]
    handle = CRMSyncerHandle(mock_crm_backend)

    handle.bulk_sync_account_access(user_ids)

    mock_crm_backend.bulk_sync_account_access.assert_called_once_with(
        user_ids, None, 300, False
    )


def test_messaging_handle_initialization(mock_messaging_backend):
    mock_messaging_backend.source = IntegrationSource.SLACK
    handle = MessagingHandle(mock_messaging_backend)

    assert handle._backend == mock_messaging_backend
    assert handle.source == IntegrationSource.SLACK


def test_messaging_handle_search_channel_messages(mock_messaging_backend):
    expected_messages = [("doc1", 0.9), ("doc2", 0.8)]
    mock_messaging_backend.search_channel_messages.return_value = expected_messages
    handle = MessagingHandle(mock_messaging_backend)

    result = handle.search_channel_messages("channel123", "test query", limit=5)

    mock_messaging_backend.search_channel_messages.assert_called_once_with(
        "channel123", "test query", 5
    )
    assert result == expected_messages


def test_messaging_handle_search_channel_messages_defaults(mock_messaging_backend):
    handle = MessagingHandle(mock_messaging_backend)

    handle.search_channel_messages("channel123", "test query")

    mock_messaging_backend.search_channel_messages.assert_called_once_with(
        "channel123", "test query", 10
    )


def test_messaging_ingestor_handle_initialization(mock_messaging_backend):
    mock_messaging_backend.source = IntegrationSource.SLACK
    handle = MessagingIngestorHandle(mock_messaging_backend)

    assert handle._backend == mock_messaging_backend
    assert handle.source == IntegrationSource.SLACK


def test_messaging_ingestor_handle_start_channel_ingestion(mock_messaging_backend):
    channel_ids = ["channel1", "channel2"]
    expected_result = {"status": "started", "job_id": "ingestion123"}
    mock_messaging_backend.start_channel_ingestion.return_value = expected_result
    handle = MessagingIngestorHandle(mock_messaging_backend)

    result = handle.start_channel_ingestion(
        channel_ids,
        interval_seconds=600,
        lookback_days=14,
        batch_size=200,
        daemon_mode=True,
        sequential_execution=True,
    )

    mock_messaging_backend.start_channel_ingestion.assert_called_once_with(
        channel_ids, 600, 14, 200, True, True
    )
    assert result == expected_result


def test_messaging_ingestor_handle_start_channel_ingestion_defaults(
    mock_messaging_backend,
):
    channel_ids = ["channel1", "channel2"]
    handle = MessagingIngestorHandle(mock_messaging_backend)

    handle.start_channel_ingestion(channel_ids)

    mock_messaging_backend.start_channel_ingestion.assert_called_once_with(
        channel_ids, 300, 7, 100, False, False
    )


def test_messaging_processor_handle_initialization(mock_messaging_backend):
    mock_messaging_backend.source = IntegrationSource.SLACK
    handle = MessagingProcessorHandle(mock_messaging_backend)

    assert handle._backend == mock_messaging_backend
    assert handle.source == IntegrationSource.SLACK


def test_messaging_processor_handle_start_channel_processing(mock_messaging_backend):
    channel_ids = ["channel1", "channel2"]
    expected_result = {"status": "started", "job_id": "processing123"}
    mock_messaging_backend.start_channel_processing.return_value = expected_result
    handle = MessagingProcessorHandle(mock_messaging_backend)

    result = handle.start_channel_processing(
        channel_ids,
        interval_seconds=600,
        batch_size=200,
        daemon_mode=True,
        sequential_execution=True,
    )

    mock_messaging_backend.start_channel_processing.assert_called_once_with(
        channel_ids, 600, 200, True, True
    )
    assert result == expected_result


def test_messaging_processor_handle_start_channel_processing_defaults(
    mock_messaging_backend,
):
    channel_ids = ["channel1", "channel2"]
    handle = MessagingProcessorHandle(mock_messaging_backend)

    handle.start_channel_processing(channel_ids)

    mock_messaging_backend.start_channel_processing.assert_called_once_with(
        channel_ids, 300, 100, False, False
    )

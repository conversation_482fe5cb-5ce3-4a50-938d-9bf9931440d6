from enum import Enum
from typing import Any, Literal

from app.common.helpers.logger import get_logger
from app.integrations.adapters.salesforce.access_resolver import (
    SalesforceAccountAccessResolver,
)
from app.integrations.adapters.salesforce.refreshable_client_mixin import (
    SalesforceRefreshableClientMixin,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
)
from app.integrations.schemas import CRMAccountAccessData

logger = get_logger()


class SalesforceObjectType(str, Enum):
    ACCOUNT = "Account"
    OPPORTUNITY = "Opportunity"


class SalesforceHandler(SalesforceRefreshableClientMixin):
    def __init__(self, credentials: ICredentials):
        self.init_salesforce_client(credentials)

    @SalesforceRefreshableClientMixin.handle_expired_session
    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return self.salesforce_client.get_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        self.salesforce_client.update_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id, fields
        )

        updated_opportunity = self.salesforce_client.get_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id
        )

        return updated_opportunity

    @SalesforceRefreshableClientMixin.handle_expired_session
    def list_opportunities_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        where_clause = f"AccountId = '{account_id}'"

        return self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.OPPORTUNITY.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def get_account(self, account_id: str) -> dict[str, Any]:
        return self.salesforce_client.get_object(
            SalesforceObjectType.ACCOUNT.value, account_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def update_account(self, account_id: str, fields: dict[str, Any]) -> dict[str, Any]:
        self.salesforce_client.update_object(
            SalesforceObjectType.ACCOUNT.value, account_id, fields
        )

        updated_account = self.salesforce_client.get_object(
            SalesforceObjectType.ACCOUNT.value, account_id
        )

        return updated_account

    @SalesforceRefreshableClientMixin.handle_expired_session
    def resolve_account_access(
        self, salesforce_user_id: str
    ) -> list[CRMAccountAccessData]:
        resolver = SalesforceAccountAccessResolver(client=self.salesforce_client)
        return resolver.get_user_account_access(salesforce_user_id)

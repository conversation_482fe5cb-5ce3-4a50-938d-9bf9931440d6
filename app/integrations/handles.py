from collections.abc import Callable
from typing import Any

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.protocols import (
    CRMResource,
    CRMSyncerResource,
    MessagingIngestorResource,
    MessagingProcessorResource,
    MessagingResource,
)
from app.integrations.schemas import DocumentData
from app.integrations.types import IntegrationSource


class BaseHandle:
    def __init__(self, backend):
        self._backend = backend

    @property
    def source(self) -> IntegrationSource:
        return self._backend.source


class CRMHandle(BaseHandle, CRMResource):
    def __init__(self, backend: BaseCRMBackend):
        super().__init__(backend)

    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return self._backend.get_opportunity(opportunity_id)

    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return self._backend.update_opportunity(opportunity_id, fields)

    def list_opportunities_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return self._backend.list_opportunities_by_account(account_id, limit, offset)

    def get_account(self, account_id: str) -> dict[str, Any]:
        return self._backend.get_account(account_id)

    def update_account(self, account_id: str, fields: dict[str, Any]) -> dict[str, Any]:
        return self._backend.update_account(account_id, fields)

    def list_account_access(
        self, crm_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return self._backend.list_account_access(crm_user_id, limit, offset)


class CRMSyncerHandle(BaseHandle, CRMSyncerResource):
    def __init__(self, backend: BaseCRMBackend):
        super().__init__(backend)

    def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return self._backend.bulk_sync_account_access(
            crm_user_ids, get_credentials_resolver, interval_seconds, daemon_mode
        )


class MessagingHandle(BaseHandle, MessagingResource):
    def __init__(self, backend: BaseMessagingBackend):
        super().__init__(backend)

    def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        return self._backend.search_channel_messages(channel_id, query, limit)


class MessagingIngestorHandle(BaseHandle, MessagingIngestorResource):
    def __init__(self, backend: BaseMessagingBackend):
        super().__init__(backend)

    def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        return self._backend.start_channel_ingestion(
            channel_ids,
            interval_seconds,
            lookback_days,
            batch_size,
            daemon_mode,
            sequential_execution,
        )


class MessagingProcessorHandle(BaseHandle, MessagingProcessorResource):
    def __init__(self, backend: BaseMessagingBackend):
        super().__init__(backend)

    def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        return self._backend.start_channel_processing(
            channel_ids, interval_seconds, batch_size, daemon_mode, sequential_execution
        )

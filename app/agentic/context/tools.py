import asyncio
import inspect
from abc import ABC, abstractmethod
from collections.abc import Awaitable, Callable
from typing import Any, Literal, cast
from uuid import UUID

from langchain_linkup import LinkupSearchTool
from pydantic import BaseModel

from app.agentic.context.schemas import (
    GetAccount,
    GetOpportunity,
    ListOpportunitiesByAccount,
    ToolDefinition,
    UpdateAccount,
    UpdateOpportunity,
)
from app.core.config import config
from app.workspace.integrations.user_integrations import UserIntegrations


class IToolBuilder(ABC):
    @abstractmethod
    def build_tools(self) -> list[ToolDefinition]:
        pass


class WebSearchToolBuilder(IToolBuilder):
    def __init__(
        self,
        api_key: str = config.linkup_api_key,
        depth: Literal["standard", "deep"] = "standard",
        output_type: Literal[
            "searchResults", "sourcedAnswer", "structured"
        ] = "searchResults",
    ):
        self.api_key = api_key
        self.depth = depth
        self.output_type = output_type

    def build_tools(self) -> list[ToolDefinition]:
        linkup_tool = LinkupSearchTool(
            depth=self.depth,
            output_type=self.output_type,
            linkup_api_key=self.api_key,
        )

        async def search_web(query: str) -> str:
            result = linkup_tool.invoke({"query": query})

            if hasattr(result, "results") and result.results:
                search_results = []
                for item in result.results:
                    search_results.append(
                        f"Title: {item.name}\nURL: {item.url}\nContent: {item.content}\n"
                    )
                return "\n\n".join(search_results)
            return "No search results found."

        return [
            ToolDefinition(
                name="search_web",
                coroutine=search_web,
                description=linkup_tool.description,
                args_schema=cast("type[BaseModel]", linkup_tool.args_schema),
            )
        ]


class CRMToolBuilder(IToolBuilder):
    CRM_METHODS = [
        ("get_opportunity", "Fetch a CRM opportunity by its ID", GetOpportunity),
        (
            "update_opportunity",
            "Update a CRM opportunity with provided fields",
            UpdateOpportunity,
        ),
        (
            "list_opportunities_by_account",
            "List CRM opportunities for a given account",
            ListOpportunitiesByAccount,
        ),
        ("get_account", "Fetch a CRM account by its ID", GetAccount),
        ("update_account", "Update a CRM account with provided fields", UpdateAccount),
    ]

    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations

    def build_tools(self) -> list[ToolDefinition]:
        crm = self.user_integrations.crm()

        if not crm:
            raise RuntimeError(f"No CRM integration configured for user {self.user_id}")

        tools = []
        for method_name, description, schema_class in self.CRM_METHODS:
            method = getattr(crm, method_name)
            coroutine = self._ensure_async(method)
            tools.append(
                ToolDefinition(
                    name=method_name,
                    coroutine=coroutine,
                    description=description,
                    args_schema=cast("type[BaseModel]", schema_class),
                )
            )

        return tools

    @staticmethod
    def _ensure_async(fn: Callable[..., Any]) -> Callable[..., Awaitable[Any]]:
        if inspect.iscoroutinefunction(fn):
            return fn

        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            return await asyncio.to_thread(fn, *args, **kwargs)

        return wrapper


class ToolRegistry:
    def __init__(self):
        self.builders: list[IToolBuilder] = []

    def register_builder(self, builder: IToolBuilder) -> None:
        self.builders.append(builder)

    def build_all_tools(self) -> list[ToolDefinition]:
        all_tools = []
        for builder in self.builders:
            all_tools.extend(builder.build_tools())
        return all_tools


def get_tools(
    user_id: UUID,
    user_integrations: UserIntegrations,
) -> list[ToolDefinition]:
    registry = ToolRegistry()

    registry.register_builder(WebSearchToolBuilder())
    registry.register_builder(CRMToolBuilder(user_id, user_integrations))

    return registry.build_all_tools()

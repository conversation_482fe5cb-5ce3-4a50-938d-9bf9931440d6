import json
from collections.abc import AsyncIterator, Callable, Mapping
from enum import Enum
from typing import Any

from langchain_core.messages import AI<PERSON>essage, AIMessageChunk, HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.postgres import CheckpointTuple
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph import StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, Interrupt

from app.agentic.schemas import (
    PaginationInfo,
    ThreadHistoryResponse,
    ThreadMessage,
)


class SseEventType(Enum):
    METADATA = "metadata"
    MESSAGE = "message"
    TOOL_START = "tool_start"
    TOOL_END = "tool_end"
    INTERRUPT = "interrupt"
    RESUME = "resume"
    PAGINATION_INFO = "pagination_info"


class GraphInstance:
    def __init__(self, checkpointer: AsyncPostgresSaver, graph_definition: StateGraph):
        self.graph: CompiledStateGraph = graph_definition.compile(
            checkpointer=checkpointer
        )
        self._event_handlers: dict[
            str, Callable[[Mapping[str, Any]], AsyncIterator[str]]
        ] = {
            "on_chat_model_stream": self._handle_chat_model_stream,
            "on_tool_start": self._handle_tool_start,
            "on_tool_end": self._handle_tool_end,
            "on_chain_stream": self._handle_chain_stream,
        }

    @staticmethod
    def _format_sse_event(
        data: ToolMessage | Interrupt | Mapping[str, Any] | str,
        event_type: SseEventType = SseEventType.MESSAGE,
    ) -> str:
        """
        Format the data into an SSE event string.
        See https://ai-sdk.dev/docs/ai-sdk-ui/stream-protocol#data-stream-protocol
        """
        payload: dict[str, Any] | list[dict[str, Any]] | str
        if event_type == SseEventType.TOOL_START:
            assert isinstance(data, dict)
            payload = {
                "toolName": data["name"],
                "toolCallId": data["run_id"],
                "args": data["data"]["input"],
            }
            data_stream_event_type = "9"
        elif event_type == SseEventType.TOOL_END:
            assert isinstance(data, dict)
            assert isinstance(data["data"]["output"], ToolMessage)
            payload = {
                "toolName": data["name"],
                "toolCallId": data["run_id"],
                "result": data["data"]["output"].content,
            }
            data_stream_event_type = "a"
        elif isinstance(data, Interrupt):
            payload = [
                {
                    "value": data.value,
                    "resumable": data.resumable,
                    "ns": data.ns,
                }
            ]
            data_stream_event_type = "2"

        elif isinstance(data, str):
            payload = data
            data_stream_event_type = "0"
        else:
            assert isinstance(data, dict), f"Unexpected data type: {data}"
            payload = [data]
            data_stream_event_type = "2"

        return f"{data_stream_event_type}:{json.dumps(payload)}\n"

    @staticmethod
    def _get_config(thread_id: str, checkpoint_ns: str = "") -> RunnableConfig:
        configurable = {
            "thread_id": thread_id,
            "checkpoint_ns": checkpoint_ns,
        }

        return RunnableConfig(configurable=configurable)

    async def _handle_chat_model_stream(
        self, event: Mapping[str, Any]
    ) -> AsyncIterator[str]:
        chunk = event.get("data", {}).get("chunk")

        if isinstance(chunk, AIMessageChunk) and isinstance(chunk.content, str):
            if chunk.content.strip():
                yield self._format_sse_event(chunk.content, SseEventType.MESSAGE)

    async def _handle_tool_start(self, event: Mapping[str, Any]) -> AsyncIterator[str]:
        yield self._format_sse_event(
            event,
            event_type=SseEventType.TOOL_START,
        )

    async def _handle_tool_end(self, event: Mapping[str, Any]) -> AsyncIterator[str]:
        yield self._format_sse_event(
            event,
            event_type=SseEventType.TOOL_END,
        )

    async def _handle_chain_stream(
        self, event: Mapping[str, Any]
    ) -> AsyncIterator[str]:
        chunk = event.get("data", {}).get("chunk")

        if isinstance(chunk, Command):
            message_content = str(chunk.update) if chunk.update is not None else ""
            message_data = message_content
            yield self._format_sse_event(message_data, event_type=SseEventType.RESUME)

        elif isinstance(chunk, dict) and "__interrupt__" in chunk:
            interrupt_value = chunk["__interrupt__"]
            if isinstance(interrupt_value, tuple) and isinstance(
                interrupt_value[0], Interrupt
            ):
                yield self._format_sse_event(
                    interrupt_value[0],
                    event_type=SseEventType.INTERRUPT,
                )

    async def stream_graph(self, graph_input_raw: dict[str, Any]) -> AsyncIterator[str]:
        config = self._get_config(graph_input_raw["thread_id"])

        graph_input: dict[str, Any] | Command
        if graph_input_raw.get("resume") is not None:
            graph_input = Command(resume=graph_input_raw["resume"])
        else:
            graph_input = graph_input_raw

        stream = self.graph.astream_events(
            graph_input,
            config=config,
            version="v2",
        )

        async for event in stream:
            kind = event["event"]
            handler = self._event_handlers.get(kind)
            if handler:
                async for sse_event_str in handler(event):
                    yield sse_event_str

    @staticmethod
    def _parse_historical_message_data(
        lc_message: AIMessage | HumanMessage | ToolMessage,
    ) -> dict[str, Any] | None:
        if not hasattr(lc_message, "content"):
            return None

        content_text = str(lc_message.content)

        if not content_text.strip():
            return None

        if isinstance(lc_message, HumanMessage):
            role = "user"
        elif isinstance(lc_message, AIMessage):
            role = "assistant"
        elif isinstance(lc_message, ToolMessage):
            return None

        parsed_data: dict[str, Any] = {
            "id": lc_message.id,
            "role": role,
            "content": {"type": "text", "text": content_text},
        }

        return parsed_data

    async def _get_latest_checkpoint_messages(
        self, thread_id: str
    ) -> list[AIMessage | HumanMessage | ToolMessage] | None:
        runnable_config = self._get_config(thread_id)
        latest_checkpoint_tuple: CheckpointTuple | None = None

        checkpointer = self.graph.checkpointer
        if not isinstance(checkpointer, AsyncPostgresSaver):
            return None

        async for cpt in checkpointer.alist(config=runnable_config, limit=1):
            latest_checkpoint_tuple = cpt
            break

        if not latest_checkpoint_tuple:
            return None

        checkpoint_content = latest_checkpoint_tuple.checkpoint
        channel_values = checkpoint_content.get("channel_values", {})
        return channel_values.get("messages", [])

    @staticmethod
    def _get_paginated_messages(
        all_messages: list[AIMessage | HumanMessage | ToolMessage],
        page: int,
        size: int,
    ) -> list[AIMessage | HumanMessage | ToolMessage]:
        start_index = (page - 1) * size
        end_index = start_index + size
        return all_messages[start_index:end_index]

    async def get_historical_messages(
        self, thread_id: str, page: int, size: int
    ) -> ThreadHistoryResponse | None:
        all_langchain_messages = await self._get_latest_checkpoint_messages(thread_id)

        if not all_langchain_messages:
            return None

        total_messages = len(all_langchain_messages)
        total_pages = (total_messages + size - 1) // size

        pagination_info = PaginationInfo(
            thread_id=thread_id,
            current_page=page,
            page_size=size,
            total_messages=total_messages,
            total_pages=total_pages,
        )

        paginated_messages = self._get_paginated_messages(
            all_langchain_messages, page, size
        )

        historical_messages = []
        for lc_message in paginated_messages:
            message_data = self._parse_historical_message_data(lc_message)
            if message_data:
                content = message_data["content"]["text"]
                historical_message = ThreadMessage(
                    id=message_data["id"],
                    role=message_data["role"],
                    content=content,
                )
                historical_messages.append(historical_message)

        return ThreadHistoryResponse(
            pagination=pagination_info,
            messages=historical_messages,
        )
